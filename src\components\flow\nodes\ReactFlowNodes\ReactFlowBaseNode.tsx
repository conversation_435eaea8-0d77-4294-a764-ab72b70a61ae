import React from 'react';
import { Handle, Position, type NodeProps } from '@xyflow/react';
import { styled } from '@mui/material/styles';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import BuildIcon from '@mui/icons-material/Build';
import { NodeSettingsButton } from '../BaseNode/NodeSettingsButton';
import type { BaseNodeData } from '../../../../types/baseNode.types';

interface NodeContainerProps {
  $accentColor?: string;
}

const NodeContainer = styled('div')<NodeContainerProps>(({ $accentColor }) => ({
  width: 220,
  height: 80,
  background: 'rgba(30,40,60,0.45) !important',
  border: `2.5px solid ${$accentColor || '#5b9cff'} !important`,
  borderRadius: '24px !important',
  boxShadow: `0 4px 32px 0 ${$accentColor || '#5b9cff'}33, 0 1.5px 0 #0a1f3c !important`,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'stretch',
  justifyContent: 'flex-start',
  fontWeight: 600,
  color: '#e9f0fb',
  cursor: 'grab',
  userSelect: 'none',
  fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
  transition: 'box-shadow 0.2s, border 0.2s',
  backdropFilter: 'blur(10px) saturate(180%)',
  overflow: 'hidden',
  position: 'relative',
  // Override React Flow default styles
  '&.react-flow__node': {
    background: 'transparent !important',
    border: 'none !important',
    padding: '0 !important',
    borderRadius: '0 !important',
  },
  ':hover': {
    boxShadow: `0 8px 36px 0 ${$accentColor || '#3578e5'}44, 0 1.5px 0 #3578e5 !important`,
    border: `2.5px solid ${$accentColor || '#3578e5'} !important`,
  },
}));

const StyledHandle = styled(Handle)(() => ({
  width: 16,
  height: 16,
  borderRadius: '50%',
  background: '#6db6ff',
  border: '2px solid #ffffff',
  boxShadow: '0 0 8px 2px #2ec4ff77',
  transition: 'all 0.2s ease',
  cursor: 'crosshair',
  pointerEvents: 'all',
  zIndex: 100,
  '&.react-flow__handle-connecting': {
    background: '#2ec4ff',
    border: '2px solid #ffffff',
    transform: 'scale(1.3)',
    boxShadow: '0 0 12px 4px #2ec4ff99',
  },
  '&:hover': {
    background: '#2ec4ff',
    border: '2px solid #ffffff',
    transform: 'scale(1.2)',
    boxShadow: '0 0 12px 4px #2ec4ff99',
  },
}));

// React Flow node data type - this is what goes in the 'data' property of a React Flow node
export interface ReactFlowNodeData extends BaseNodeData {
  label: string;
  accentColor?: string;
}

// Props for React Flow nodes
export type ReactFlowBaseNodeProps = NodeProps<ReactFlowNodeData>;

interface BaseNodeComponentProps {
  data: ReactFlowNodeData;
  children?: React.ReactNode;
}

export const ReactFlowBaseNode: React.FC<BaseNodeComponentProps> = ({
  data,
  children
}) => {
  const { label, accentColor = '#5b9cff', NodeType } = data;

  // Get icon for node type
  const getNodeIcon = (nodeType: string, isOutput: boolean = false) => {
    const iconProps = {
      sx: {
        fontSize: 20,
        color: isOutput ? '#1565c0' : '#2196f3'
      }
    };

    switch (nodeType) {
      case 'input':
        return <ArrowDownwardIcon {...iconProps} />;
      case 'agent':
        return <SmartToyIcon {...iconProps} />;
      case 'tool':
        return <BuildIcon {...iconProps} />;
      case 'output':
        return <ArrowUpwardIcon {...iconProps} />;
      default:
        return <span style={{fontSize: 18, color: isOutput ? '#1565c0' : '#2196f3'}}>•</span>;
    }
  };

  return (
    <div style={{
      position: 'relative',
      background: 'transparent',
      border: 'none',
      padding: 0,
      margin: 0,
    }}>
      {/* Input Handle - Show for all nodes except 'input' type */}
      {NodeType !== 'input' && (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
        />
      )}

      <NodeContainer $accentColor={accentColor}>
        {/* Node header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          padding: '10px 16px 4px 16px',
          boxSizing: 'border-box',
          borderBottom: '1.5px solid rgba(90,140,255,0.08)',
          background: 'linear-gradient(90deg, rgba(60,100,180,0.12) 0%, rgba(60,120,255,0.10) 100%)',
          fontSize: 16,
          fontWeight: 700,
          letterSpacing: 0.3,
          textShadow: '0 1px 2px #0a1f3c22',
          color: '#eaf6ff',
        }}>
          <span style={{ 
            fontSize: 16, 
            fontWeight: 700, 
            letterSpacing: 0.3, 
            color: '#eaf6ff', 
            textShadow: '0 1px 2px #0a1f3c22' 
          }}>
            {label}
          </span>
          <div style={{ position: 'relative', zIndex: 2 }}>
            <NodeSettingsButton 
              nodeType={NodeType}
              onSettingsClick={() => {}}
            />
          </div>
        </div>
        
        {/* Node body */}
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0 16px',
          color: '#b5c8e8',
          fontSize: 13,
          background: 'transparent',
          position: 'relative',
          minHeight: 40,
        }}>
          {children}
        </div>
      </NodeContainer>

      {/* Output Handle - Show for all nodes except 'output' type */}
      {NodeType !== 'output' && (
        <Handle
          type="source"
          position={Position.Right}
          id="output"
        />
      )}
    </div>
  );
};
