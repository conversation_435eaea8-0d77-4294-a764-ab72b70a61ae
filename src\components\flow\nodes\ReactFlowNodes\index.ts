export { ReactFlowBaseNode } from './ReactFlowBaseNode';
export { ReactFlowInputNode } from './ReactFlowInputNode';
export { ReactFlowAgentNode } from './ReactFlowAgentNode';
export { ReactFlowToolNode } from './ReactFlowToolNode';
export { ReactFlowOutputNode } from './ReactFlowOutputNode';

export type { ReactFlowNodeData, ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

// Node types mapping for React Flow
export const nodeTypes = {
  input: ReactFlowInputNode,
  agent: ReactFlowAgentNode,
  tool: ReactFlowToolNode,
  output: ReactFlowOutputNode,
};
