import { useState, useCallback, useEffect } from 'react';

import type { BaseNodeData } from '../types/baseNode.types';

const initialNodes: BaseNodeData[] = [
  { NodeId: '1', position: { x: 100, y: 100 }, NodeType: 'input', label: 'Input' },
  { NodeId: '2', position: { x: 300, y: 200 }, NodeType: 'agent', label: 'Agent' }
];

export function useFlowNodes() {
  const [nodes, setNodes] = useState<BaseNodeData[]>(initialNodes);
  
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);
  const [offset, setOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  const onMouseDown = useCallback((e: React.MouseEvent, NodeId: string) => {
    setDraggedNodeId(NodeId);
    const node = nodes.find(n => n.NodeId === NodeId);
    if (node) {
      setOffset({ x: e.clientX - node.position.x, y: e.clientY - node.position.y });
    }
  }, [nodes]);

  const onMouseMove = useCallback((e: MouseEvent) => {
    if (draggedNodeId) {
      setNodes(nodes =>
        nodes.map(n =>
          n.NodeId === draggedNodeId
            ? {
                ...n,
                position: {
                  x: e.clientX - offset.x,
                  y: e.clientY - offset.y,
                },
              }
            : n
        )
      );
    }
  }, [draggedNodeId, offset]);

  useEffect(() => {
    if (!draggedNodeId) return;

    const handleMouseMove = (e: MouseEvent) => {
      setNodes(nodes =>
        nodes.map(n =>
          n.NodeId === draggedNodeId
            ? {
                ...n,
                position: {
                  x: e.clientX - offset.x,
                  y: e.clientY - offset.y,
                },
              }
            : n
        )
      );
    };

    const handleMouseUp = () => setDraggedNodeId(null);

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [draggedNodeId, offset]);

  return { nodes, setNodes, onMouseDown };
}
