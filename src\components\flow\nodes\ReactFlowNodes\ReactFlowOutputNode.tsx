import React from 'react';
import { ReactFlowBaseNode, ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowOutputNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Output',
    accentColor: '#ff6978',
    NodeType: 'output' as const
  };

  return (
    <ReactFlowBaseNode 
      data={nodeData} 
      selected={props.selected}
    >
      {/* Output node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
