import React from 'react';
import { ReactFlowBaseNode, ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowAgentNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Agent',
    accentColor: '#47c78a',
    NodeType: 'agent' as const
  };

  return (
    <ReactFlowBaseNode 
      data={nodeData} 
      selected={props.selected}
    >
      {/* Agent node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
