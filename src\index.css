/* React Flow CSS Overrides */
.react-flow__node {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node-input,
.react-flow__node-agent,
.react-flow__node-tool,
.react-flow__node-output {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node.selected {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Ensure handles are properly positioned and interactive */
.react-flow__handle {
  position: absolute !important;
  z-index: 100 !important;
  pointer-events: all !important;
  cursor: crosshair !important;
}

.react-flow__handle:hover {
  transform: scale(1.1) !important;
}

.react-flow__handle.react-flow__handle-connecting {
  transform: scale(1.2) !important;
}