/* React Flow CSS Overrides */
.react-flow__node {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node-input,
.react-flow__node-agent,
.react-flow__node-tool,
.react-flow__node-output {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.react-flow__node.selected {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Ensure handles are properly positioned */
.react-flow__handle {
  position: absolute !important;
  z-index: 10 !important;
}