import { Node, Edge } from '@xyflow/react';
import type { BaseNodeData } from '../types/baseNode.types';
import type { ReactFlowNodeData } from '../components/flow/nodes/ReactFlowNodes';

/**
 * Convert BaseNodeData to React Flow Node format
 */
export function baseNodeToReactFlowNode(baseNode: BaseNodeData): Node<ReactFlowNodeData> {
  return {
    id: baseNode.NodeId,
    type: baseNode.NodeType,
    position: baseNode.position,
    data: {
      ...baseNode,
      label: baseNode.label,
      accentColor: baseNode.accentColor,
    },
    dragHandle: '.react-flow__node',
  };
}

/**
 * Convert React Flow Node back to BaseNodeData format
 */
export function reactFlowNodeToBaseNode(reactFlowNode: Node<ReactFlowNodeData>): BaseNodeData {
  return {
    ...reactFlowNode.data,
    NodeId: reactFlowNode.id,
    position: reactFlowNode.position,
  };
}

/**
 * Convert BaseNodeData array to React Flow Nodes
 */
export function baseNodesToReactFlowNodes(baseNodes: BaseNodeData[]): Node<ReactFlowNodeData>[] {
  return baseNodes.map(baseNodeToReactFlowNode);
}

/**
 * Convert React Flow Nodes back to BaseNodeData array
 */
export function reactFlowNodesToBaseNodes(reactFlowNodes: Node<ReactFlowNodeData>[]): BaseNodeData[] {
  return reactFlowNodes.map(reactFlowNodeToBaseNode);
}

/**
 * Generate React Flow Edges from BaseNodeData connections
 */
export function generateEdgesFromBaseNodes(baseNodes: BaseNodeData[]): Edge[] {
  const edges: Edge[] = [];
  
  baseNodes.forEach(node => {
    if (node.OutputDestination) {
      const targetNode = baseNodes.find(n => n.NodeId === node.OutputDestination);
      if (targetNode) {
        edges.push({
          id: `${node.NodeId}-${node.OutputDestination}`,
          source: node.NodeId,
          target: node.OutputDestination,
          type: 'smoothstep',
          style: {
            stroke: '#5b9cff',
            strokeWidth: 3,
          },
          markerEnd: {
            type: 'arrowclosed',
            color: '#5b9cff',
            width: 20,
            height: 20,
          },
          animated: false,
        });
      }
    }
  });
  
  return edges;
}

/**
 * Update BaseNodeData connections from React Flow Edges
 */
export function updateBaseNodesFromEdges(
  baseNodes: BaseNodeData[], 
  edges: Edge[]
): BaseNodeData[] {
  // First, clear all existing connections
  const clearedNodes = baseNodes.map(node => ({
    ...node,
    OutputDestination: undefined,
    SourceNode: undefined,
  }));
  
  // Then, apply connections from edges
  return clearedNodes.map(node => {
    const outgoingEdge = edges.find(edge => edge.source === node.NodeId);
    const incomingEdge = edges.find(edge => edge.target === node.NodeId);
    
    return {
      ...node,
      OutputDestination: outgoingEdge?.target,
      SourceNode: incomingEdge?.source,
    };
  });
}
