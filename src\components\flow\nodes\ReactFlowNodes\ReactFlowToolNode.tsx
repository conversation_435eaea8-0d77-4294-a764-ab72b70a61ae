import React from 'react';
import { ReactFlowBaseNode, ReactFlowBaseNodeProps } from './ReactFlowBaseNode';

export const ReactFlowToolNode: React.FC<ReactFlowBaseNodeProps> = (props) => {
  const nodeData = {
    ...props.data,
    label: 'Tool',
    accentColor: '#f8b84e',
    NodeType: 'tool' as const
  };

  return (
    <ReactFlowBaseNode 
      data={nodeData} 
      selected={props.selected}
    >
      {/* Tool node specific content can go here */}
    </ReactFlowBaseNode>
  );
};
