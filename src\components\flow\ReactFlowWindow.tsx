import React, { use<PERSON>allback, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  BackgroundVariant,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { styled } from '@mui/material/styles';

import { nodeTypes } from './nodes/ReactFlowNodes';
import type { ReactFlowNodeData } from './nodes/ReactFlowNodes';
import FlowMenu from './FlowMenu/FlowMenu';
import { FlowMenuToolbar } from './toolbar/Toolbar';
import { useFlowMenu } from '../../hooks/useFlowMenu';
import { useReactFlowAddNode } from '../../hooks/useReactFlowNodes';
import {
  baseNodesToReactFlowNodes,
  generateEdgesFromBaseNodes,
  reactFlowNodesToBaseNodes,
  updateBaseNodesFromEdges
} from '../../utils/reactFlowConverters';
import type { BaseNodeData } from '../../types/baseNode.types';

const FlowContainer = styled('div')({
  position: 'relative',
  width: '100%',
  height: '100%',
  background: 'linear-gradient(135deg, #23272f 0%, #2c3340 100%)',
  overflow: 'hidden',
  minHeight: 600,
});

const ReactFlowWrapper = styled('div')({
  width: '100%',
  height: '100%',
  '& .react-flow__background': {
    backgroundColor: 'transparent',
  },
  '& .react-flow__controls': {
    background: 'rgba(30,40,60,0.8)',
    border: '1px solid rgba(90,140,255,0.2)',
    borderRadius: 12,
    '& button': {
      background: 'rgba(60,100,180,0.1)',
      border: 'none',
      color: '#e9f0fb',
      '&:hover': {
        background: 'rgba(60,100,180,0.2)',
      },
    },
  },
  '& .react-flow__minimap': {
    background: 'rgba(30,40,60,0.8)',
    border: '1px solid rgba(90,140,255,0.2)',
    borderRadius: 8,
  },
});

// Initial nodes for demo
const initialBaseNodes: BaseNodeData[] = [
  { 
    NodeId: '1', 
    position: { x: 100, y: 100 }, 
    NodeType: 'input', 
    label: 'Input',
    accentColor: '#4e8cff'
  },
  { 
    NodeId: '2', 
    position: { x: 400, y: 200 }, 
    NodeType: 'agent', 
    label: 'Agent',
    accentColor: '#47c78a'
  }
];

const ReactFlowWindow: React.FC = () => {
  const { expanded: menuExpanded, toggle: toggleMenu } = useFlowMenu(true);
  
  // Convert initial base nodes to React Flow format
  const initialNodes = useMemo(() => baseNodesToReactFlowNodes(initialBaseNodes), []);
  const initialEdges = useMemo(() => generateEdgesFromBaseNodes(initialBaseNodes), []);
  
  const [nodes, setNodes, onNodesChange] = useNodesState<ReactFlowNodeData>(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Convert React Flow nodes back to BaseNodeData for compatibility
  const baseNodes = useMemo(() => reactFlowNodesToBaseNodes(nodes), [nodes]);

  // Add node functionality
  const addNodeHandler = useReactFlowAddNode(setNodes);

  const addNode = useCallback((type: string) => {
    const nodeTypeMap: Record<string, 'input' | 'agent' | 'tool' | 'output'> = {
      'input': 'input',
      'agent': 'agent',
      'tool': 'tool',
      'output': 'output'
    };

    const nodeType = nodeTypeMap[type];
    if (nodeType) {
      addNodeHandler(nodeType);
    }
  }, [addNodeHandler]);

  // Handle new connections
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge = {
        ...params,
        id: `${params.source}-${params.target}`,
        type: 'smoothstep',
        style: {
          stroke: '#5b9cff',
          strokeWidth: 3,
        },
        markerEnd: {
          type: 'arrowclosed',
          color: '#5b9cff',
          width: 20,
          height: 20,
        },
        animated: false,
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  return (
    <FlowContainer>
      <FlowMenu expanded={menuExpanded} onToggle={toggleMenu} onAddNode={addNode} />
      
      <ReactFlowWrapper>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
        >
          <Background 
            variant={BackgroundVariant.Dots} 
            gap={32} 
            size={1}
            color="rgba(255,255,255,0.04)"
          />
          <Controls />
          <MiniMap 
            nodeColor={(node) => {
              const data = node.data as ReactFlowNodeData;
              return data.accentColor || '#5b9cff';
            }}
            maskColor="rgba(30,40,60,0.6)"
          />
        </ReactFlow>
      </ReactFlowWrapper>

      <div
        style={{
          position: 'absolute',
          left: 0,
          bottom: 0,
          width: menuExpanded ? 'calc(100% - 280px)' : '100%',
          right: menuExpanded ? 280 : 0,
          zIndex: 20,
          transition: 'width 0.3s cubic-bezier(.4,1.2,.6,1), right 0.3s cubic-bezier(.4,1.2,.6,1)',
        }}
      >
        <FlowMenuToolbar expanded={menuExpanded} />
      </div>
    </FlowContainer>
  );
};

export default ReactFlowWindow;
